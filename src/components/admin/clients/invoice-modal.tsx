'use client'

import {
    DocumentTextIcon,
    PlusIcon,
    TrashIcon,
    XMarkIcon
} from '@heroicons/react/24/outline'
import { motion } from 'framer-motion'
import React, { useEffect, useState } from 'react'

interface InvoiceItem {
  id?: string
  description: string
  quantity: number
  unitPrice: number
  totalPrice: number
}

interface InvoiceModalProps {
  isOpen: boolean
  onClose: () => void
  onSubmit: (data: any) => Promise<void>
  title: string
  initialData?: any
  client: {
    id: string | number
    companyName: string
    contactName: string
    contactEmail: string
  }
  project: {
    id: string | number
    name: string
    description: string
  }
}

export function InvoiceModal({
  isOpen,
  onClose,
  onSubmit,
  title,
  initialData,
  client,
  project
}: InvoiceModalProps) {
  const [loading, setLoading] = useState(false)
  const [itemsLoading, setItemsLoading] = useState(false)
  const [formData, setFormData] = useState({
    dueDate: '',
    status: 'DRAFT',
    description: '',
    taxRate: 0,
    subtotal: 0,
    taxAmount: 0,
    totalAmount: 0,
    paidAt: ''
  })

  const [items, setItems] = useState<InvoiceItem[]>([
    { id: 'temp-default', description: '', quantity: 1, unitPrice: 0, totalPrice: 0 }
  ])

  // Calculate totals when items or tax rate changes
  useEffect(() => {
    const subtotal = items.reduce((sum, item) => sum + item.totalPrice, 0)
    const taxAmount = (subtotal * formData.taxRate) / 100
    const totalAmount = subtotal + taxAmount

    setFormData(prev => ({
      ...prev,
      subtotal,
      taxAmount,
      totalAmount
    }))
  }, [items, formData.taxRate])

  // Load initial data
  useEffect(() => {
    const loadInvoiceData = async () => {
      console.log('loadInvoiceData called with initialData:', initialData)
      if (initialData) {
        setFormData({
          dueDate: initialData.dueDate ? new Date(initialData.dueDate).toISOString().split('T')[0] : '',
          status: initialData.status || 'DRAFT',
          description: initialData.description || '',
          taxRate: Number(initialData.taxRate) || 0,
          subtotal: Number(initialData.subtotal) || 0,
          taxAmount: Number(initialData.taxAmount) || 0,
          totalAmount: Number(initialData.totalAmount) || 0,
          paidAt: initialData.paidAt ? new Date(initialData.paidAt).toISOString().split('T')[0] : ''
        })

        // Load existing items from API
        try {
          setItemsLoading(true)
          const invoiceId = String(initialData.id)
          const response = await fetch(`/api/admin/invoices/${invoiceId}/items`)

          if (response.ok) {
            const result = await response.json()
            console.log('Raw API response:', result)

            if (result.success && result.data && Array.isArray(result.data) && result.data.length > 0) {
              console.log('Raw items data:', result.data)

              // Map database items to form items
              const mappedItems = result.data.map((item: any) => {
                console.log('Mapping item:', item)

                // Handle Decimal conversion properly
                const quantity = typeof item.quantity === 'string' ? parseFloat(item.quantity) : Number(item.quantity) || 1
                const unitPrice = typeof item.unitprice === 'string' ? parseFloat(item.unitprice) : Number(item.unitprice) || 0
                const totalPrice = typeof item.totalprice === 'string' ? parseFloat(item.totalprice) : Number(item.totalprice) || 0

                const mapped = {
                  id: String(item.id),
                  description: String(item.description || ''),
                  quantity: quantity,
                  unitPrice: unitPrice,
                  totalPrice: totalPrice
                }
                console.log('Mapped to:', mapped)
                return mapped
              })

              console.log('Final mapped items:', mappedItems)
              setItems(mappedItems)
            } else {
              // No items found, use default empty item
              setItems([{ id: 'temp-default', description: '', quantity: 1, unitPrice: 0, totalPrice: 0 }])
            }
          } else {
            // API error, use default empty item
            console.error('Failed to fetch invoice items:', response.status, response.statusText)
            setItems([{ id: 'temp-default', description: '', quantity: 1, unitPrice: 0, totalPrice: 0 }])
          }
        } catch (error) {
          // Network or other error, use default empty item
          console.error('Error loading invoice items:', error)
          setItems([{ id: 'temp-default', description: '', quantity: 1, unitPrice: 0, totalPrice: 0 }])
        } finally {
          setItemsLoading(false)
        }
      } else {
        // Reset form for new invoice
        setFormData({
          dueDate: '',
          status: 'DRAFT',
          description: '',
          taxRate: 0,
          subtotal: 0,
          taxAmount: 0,
          totalAmount: 0,
          paidAt: ''
        })
        setItems([{ id: 'temp-default', description: '', quantity: 1, unitPrice: 0, totalPrice: 0 }])
      }
    }

    if (isOpen) {
      loadInvoiceData()
    }
  }, [initialData, isOpen])

  const addItem = () => {
    setItems([...items, {
      id: `temp-${Date.now()}`, // Temporary ID for new items
      description: '',
      quantity: 1,
      unitPrice: 0,
      totalPrice: 0
    }])
  }

  const removeItem = (index: number) => {
    if (items.length > 1) {
      setItems(items.filter((_, i) => i !== index))
    }
  }

  const updateItem = (index: number, field: keyof InvoiceItem, value: any) => {
    const updatedItems = [...items]
    updatedItems[index] = { ...updatedItems[index], [field]: value }

    // Auto-calculate total price when quantity or unit price changes
    if (field === 'quantity' || field === 'unitPrice') {
      const quantity = field === 'quantity' ? Number(value) || 0 : updatedItems[index].quantity
      const unitPrice = field === 'unitPrice' ? Number(value) || 0 : updatedItems[index].unitPrice
      updatedItems[index].totalPrice = quantity * unitPrice
    }

    setItems(updatedItems)
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)

    try {
      // Validate items
      const validItems = items.filter(item => item.description.trim() !== '')

      if (validItems.length === 0) {
        alert('Please add at least one item to the invoice.')
        setLoading(false)
        return
      }

      // Validate that all items have positive quantities and prices
      const invalidItems = validItems.filter(item =>
        item.quantity <= 0 || item.unitPrice < 0
      )

      if (invalidItems.length > 0) {
        alert('All items must have positive quantities and non-negative unit prices.')
        setLoading(false)
        return
      }

      const submitData = {
        ...formData,
        items: validItems.map(item => ({
          ...item,
          // Remove temporary IDs for new items
          id: item.id?.startsWith('temp-') ? undefined : item.id
        })),
        clientId: client.id,
        projectId: project.id
      }

      await onSubmit(submitData)
      onClose()
    } catch (error) {
      console.error('Error submitting invoice:', error)
      alert('Failed to save invoice. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex items-center justify-center min-h-screen px-4">
        <div className="fixed inset-0 bg-gray-900 bg-opacity-50 backdrop-blur-sm" onClick={onClose} />

        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className="relative bg-white rounded-xl shadow-2xl w-full max-w-6xl max-h-[90vh] overflow-hidden"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 bg-gradient-to-r from-purple-600 to-blue-600">
            <div className="flex items-center space-x-3">
              <DocumentTextIcon className="h-8 w-8 text-white" />
              <div>
                <h2 className="text-2xl font-bold text-white">{title}</h2>
                <p className="text-purple-100">
                  {client.companyName} - {project.name}
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="p-2 hover:bg-white/10 rounded-lg transition-colors"
            >
              <XMarkIcon className="h-6 w-6 text-white" />
            </button>
          </div>

          {/* Content */}
          <div className="overflow-y-auto max-h-[calc(90vh-140px)]">
            <form onSubmit={handleSubmit} className="p-6 space-y-6">
              {/* Basic Invoice Info */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Due Date
                  </label>
                  <input
                    type="date"
                    required
                    value={formData.dueDate}
                    onChange={(e) => setFormData({ ...formData, dueDate: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Status
                  </label>
                  <select
                    value={formData.status}
                    onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  >
                    <option value="DRAFT">Draft</option>
                    <option value="SENT">Sent</option>
                    <option value="PAID">Paid</option>
                    <option value="OVERDUE">Overdue</option>
                  </select>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Tax Rate (%)
                  </label>
                  <input
                    type="number"
                    min="0"
                    max="100"
                    step="0.01"
                    value={formData.taxRate}
                    onChange={(e) => setFormData({ ...formData, taxRate: parseFloat(e.target.value) || 0 })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  />
                </div>
              </div>

              {/* Description */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({ ...formData, description: e.target.value })}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  placeholder="Invoice description..."
                />
              </div>

              {/* Items Section */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="text-lg font-semibold text-gray-900">Invoice Items</h3>
                    <p className="text-sm text-gray-600">
                      {items.filter(item => item.description.trim() !== '').length} item(s) added
                    </p>
                    <p className="text-xs text-gray-500">
                      Debug: {JSON.stringify(items.map(item => ({ desc: item.description, qty: item.quantity, price: item.unitPrice })))}
                    </p>
                    {initialData && (
                      <button
                        type="button"
                        onClick={async () => {
                          console.log('Testing API call for invoice:', initialData.id)
                          try {
                            const response = await fetch(`/api/admin/invoices/${initialData.id}/items`)
                            const result = await response.json()
                            console.log('Direct API test result:', result)
                            alert('Check console for API response')
                          } catch (error) {
                            console.error('API test failed:', error)
                          }
                        }}
                        className="text-xs text-blue-600 hover:text-blue-800"
                      >
                        Test API
                      </button>
                    )}
                  </div>
                  <button
                    type="button"
                    onClick={addItem}
                    className="flex items-center space-x-2 px-3 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                  >
                    <PlusIcon className="h-4 w-4" />
                    <span>Add Item</span>
                  </button>
                </div>

                <div className="bg-gray-50 rounded-lg overflow-hidden">
                  {/* Table Header */}
                  <div className="grid grid-cols-12 gap-3 p-3 bg-gray-100 border-b border-gray-200 text-sm font-medium text-gray-700">
                    <div className="col-span-5">Description</div>
                    <div className="col-span-2">Quantity</div>
                    <div className="col-span-2">Unit Price</div>
                    <div className="col-span-2">Total</div>
                    <div className="col-span-1">Action</div>
                  </div>

                  {/* Items */}
                  <div className="divide-y divide-gray-200">
                    {itemsLoading ? (
                      <div className="p-8 text-center">
                        <div className="inline-flex items-center space-x-2 text-gray-600">
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-purple-600"></div>
                          <span>Loading items...</span>
                        </div>
                      </div>
                    ) : (
                      items.map((item, index) => (
                      <div key={item.id || `item-${index}`} className="p-3 hover:bg-gray-50 transition-colors">
                        <div className="grid grid-cols-12 gap-3 items-center">
                          <div className="col-span-5">
                            <input
                              type="text"
                              required
                              value={item.description}
                              onChange={(e) => updateItem(index, 'description', e.target.value)}
                              className={`w-full px-2 py-1 text-sm border rounded focus:ring-1 focus:ring-purple-500 focus:border-purple-500 ${
                                item.description.trim() === '' ? 'border-red-300 bg-red-50' : 'border-gray-300'
                              }`}
                              placeholder="Item description"
                            />
                          </div>
                          <div className="col-span-2">
                            <input
                              type="number"
                              required
                              min="1"
                              step="1"
                              value={item.quantity}
                              onChange={(e) => updateItem(index, 'quantity', Number(e.target.value) || 1)}
                              onKeyDown={(e) => {
                                if (e.key === 'Enter') {
                                  e.preventDefault()
                                  const nextInput = e.currentTarget.parentElement?.parentElement?.nextElementSibling?.querySelector('input')
                                  if (nextInput) (nextInput as HTMLInputElement).focus()
                                }
                              }}
                              className={`w-full px-2 py-1 text-sm border rounded focus:ring-1 focus:ring-purple-500 focus:border-purple-500 ${
                                item.quantity <= 0 ? 'border-red-300 bg-red-50' : 'border-gray-300'
                              }`}
                            />
                          </div>
                          <div className="col-span-2">
                            <input
                              type="number"
                              required
                              min="0"
                              step="0.01"
                              value={item.unitPrice}
                              onChange={(e) => updateItem(index, 'unitPrice', parseFloat(e.target.value) || 0)}
                              onKeyDown={(e) => {
                                if (e.key === 'Enter') {
                                  e.preventDefault()
                                  if (index === items.length - 1) {
                                    addItem()
                                  } else {
                                    const nextRow = e.currentTarget.closest('.grid')?.parentElement?.nextElementSibling?.querySelector('input')
                                    if (nextRow) (nextRow as HTMLInputElement).focus()
                                  }
                                }
                              }}
                              className={`w-full px-2 py-1 text-sm border rounded focus:ring-1 focus:ring-purple-500 focus:border-purple-500 ${
                                item.unitPrice < 0 ? 'border-red-300 bg-red-50' : 'border-gray-300'
                              }`}
                              placeholder="0.00"
                            />
                          </div>
                          <div className="col-span-2">
                            <input
                              type="text"
                              value={`$${item.totalPrice.toFixed(2)}`}
                              readOnly
                              className="w-full px-2 py-1 text-sm border border-gray-300 rounded bg-gray-50 text-gray-600 font-medium"
                            />
                          </div>
                          <div className="col-span-1">
                            <button
                              type="button"
                              onClick={() => removeItem(index)}
                              disabled={items.length === 1}
                              className="p-1 text-red-600 hover:text-red-800 disabled:text-gray-400 disabled:cursor-not-allowed"
                            >
                              <TrashIcon className="h-4 w-4" />
                            </button>
                          </div>
                        </div>
                      </div>
                      ))
                    )}
                  </div>
                </div>

                {/* Validation Messages */}
                {items.filter(item => item.description.trim() !== '').length === 0 && (
                  <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <p className="text-sm text-yellow-800">
                      ⚠️ Please add at least one item to the invoice.
                    </p>
                  </div>
                )}

                {items.some(item => item.description.trim() !== '' && (item.quantity <= 0 || item.unitPrice < 0)) && (
                  <div className="p-3 bg-red-50 border border-red-200 rounded-lg">
                    <p className="text-sm text-red-800">
                      ❌ All items must have positive quantities and non-negative unit prices.
                    </p>
                  </div>
                )}
              </div>

              {/* Invoice Summary */}
              <div className="bg-gray-50 rounded-lg p-4 space-y-2">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Subtotal:</span>
                  <span className="font-medium">${formData.subtotal.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">Tax ({formData.taxRate}%):</span>
                  <span className="font-medium">${formData.taxAmount.toFixed(2)}</span>
                </div>
                <div className="flex justify-between text-lg font-bold border-t border-gray-200 pt-2">
                  <span>Total:</span>
                  <span>${formData.totalAmount.toFixed(2)}</span>
                </div>
              </div>

              {/* Submit Buttons */}
              <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={loading}
                  className="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  {loading ? 'Saving...' : initialData ? 'Update Invoice' : 'Create Invoice'}
                </button>
              </div>
            </form>
          </div>
        </motion.div>
      </div>
    </div>
  )
}
